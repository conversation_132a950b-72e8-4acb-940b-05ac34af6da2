# API Reference - faster_rcnn_incremental.py

## Table of Contents
1. [Core Training Functions](#core-training-functions)
2. [Inference Functions](#inference-functions)
3. [Model Management](#model-management)
4. [Dataset Classes](#dataset-classes)
5. [Utility Functions](#utility-functions)
6. [Configuration Functions](#configuration-functions)
7. [Visualization Functions](#visualization-functions)

---

## Core Training Functions

### `main_training_pipeline(dataset_path, new_classes=None, config=None)`

**Description:** Main entry point for training Faster R-CNN with incremental learning and knowledge distillation.

**Parameters:**
- `dataset_path` (str): Path to dataset directory containing 'images/' and 'annotations/' subdirectories
- `new_classes` (list, optional): List of new class names to learn. Default: `['traffic_sign']`
- `config` (dict, optional): Training configuration dictionary. Uses `get_default_config()` if None

**Returns:**
- `model` (torch.nn.Module): Trained student model
- `class_names` (list): Complete list of class names (COCO + new classes)
- `train_losses` (list): Training loss history per epoch

**Example:**
```python
model, classes, losses = main_training_pipeline(
    dataset_path='/data/custom_objects',
    new_classes=['custom_obj1', 'custom_obj2'],
    config={
        'num_epochs': 20,
        'batch_size': 4,
        'distillation_alpha': 0.3
    }
)
```

**Notes:**
- Automatically creates teacher (COCO) and student (COCO + new) models
- Implements knowledge distillation to prevent catastrophic forgetting
- Saves model checkpoints every 5 epochs
- Generates training loss visualization

---

### `train_one_epoch(model, teacher_model, data_loader, optimizer, device, epoch, distillation_alpha=0.5, distillation_temperature=4.0)`

**Description:** Train for one epoch with knowledge distillation between teacher and student models.

**Parameters:**
- `model` (torch.nn.Module): Student model (trainable)
- `teacher_model` (torch.nn.Module): Teacher model (frozen)
- `data_loader` (DataLoader): Training data loader
- `optimizer` (torch.optim.Optimizer): Optimizer for student model
- `device` (torch.device): Device to run training on
- `epoch` (int): Current epoch number
- `distillation_alpha` (float): Weight for distillation loss (0.0-1.0)
- `distillation_temperature` (float): Temperature for softmax in distillation

**Returns:**
- `avg_total_loss` (float): Average total loss for the epoch

**Loss Calculation:**
```
total_loss = (1 - α) × student_loss + α × distillation_loss
```

---

## Inference Functions

### `load_model_and_run_inference(model_path, image_path, device=None, threshold=0.3)`

**Description:** Load a trained model and run inference on a single image.

**Parameters:**
- `model_path` (str): Path to saved model file (.pth)
- `image_path` (str): Path to input image
- `device` (torch.device, optional): Device for inference. Auto-detects if None
- `threshold` (float): Confidence threshold for filtering detections

**Returns:**
- `results` (dict): Detection results containing:
  - `'boxes'`: numpy array of bounding boxes [x1, y1, x2, y2]
  - `'scores'`: numpy array of confidence scores
  - `'labels'`: numpy array of class indices
  - `'class_names'`: list of class name strings
- `original_image` (PIL.Image): Original input image
- `class_names` (list): All class names from model

**Example:**
```python
results, img, classes = load_model_and_run_inference(
    'model.pth', 'test.jpg', threshold=0.5
)

for box, score, cls in zip(results['boxes'], results['scores'], results['class_names']):
    print(f"Detected {cls} with confidence {score:.3f} at {box}")
```

---

### `load_model_and_inference_folder(model_path, folder_path, output_folder='inference_results', device=None, threshold=0.3, image_extensions=None)`

**Description:** Run inference on all images in a folder and generate visualizations.

**Parameters:**
- `model_path` (str): Path to saved model file
- `folder_path` (str): Path to folder containing images
- `output_folder` (str): Output directory for visualizations
- `device` (torch.device, optional): Device for inference
- `threshold` (float): Confidence threshold
- `image_extensions` (list, optional): Supported file extensions. Default: `['.jpg', '.jpeg', '.png', '.bmp', '.tiff']`

**Returns:**
- `results` (list): List of dictionaries, one per processed image:
  ```python
  {
      'image_path': str,
      'num_detections': int,
      'detections': dict,  # Same format as load_model_and_run_inference
      'visualization_path': str
  }
  ```

**Example:**
```python
results = load_model_and_inference_folder(
    'model.pth', 'test_images/', 'output/', threshold=0.1
)

total_detections = sum(r['num_detections'] for r in results)
print(f"Processed {len(results)} images, found {total_detections} detections")
```

---

### `predict_single_image(model, image_path, class_names, device, threshold=0.1)`

**Description:** Low-level function to make predictions on a single image.

**Parameters:**
- `model` (torch.nn.Module): Loaded model in eval mode
- `image_path` (str): Path to image file
- `class_names` (list): List of class names
- `device` (torch.device): Device for inference
- `threshold` (float): Confidence threshold

**Returns:**
- `results` (dict): Detection results
- `image` (PIL.Image): Original PIL image

---

## Model Management

### `load_trained_model(model_path, device, default_classes=None)`

**Description:** Load a trained model from checkpoint file.

**Parameters:**
- `model_path` (str): Path to model checkpoint (.pth file)
- `device` (torch.device): Device to load model on
- `default_classes` (list, optional): Default class names if not saved in checkpoint

**Returns:**
- `model` (torch.nn.Module): Loaded model in eval mode
- `class_names` (list): Class names from checkpoint or default

**Checkpoint Format:**
```python
{
    'model_state_dict': OrderedDict,  # Model weights
    'class_names': list,              # Class names
    'train_losses': list,             # Training history (optional)
    'epoch': int,                     # Epoch number (optional)
    'optimizer_state_dict': dict      # Optimizer state (optional)
}
```

---

### `create_model(num_classes, pretrained=True)`

**Description:** Create a Faster R-CNN model with ResNet-50 FPN backbone.

**Parameters:**
- `num_classes` (int): Number of output classes (including background)
- `pretrained` (bool): Use pretrained backbone weights

**Returns:**
- `model` (torch.nn.Module): Faster R-CNN model

**Architecture:**
- Backbone: ResNet-50 with Feature Pyramid Network (FPN)
- RPN: Region Proposal Network
- ROI Head: Fast R-CNN predictor with `num_classes` outputs

---

### `setup_models(num_classes, device, freeze_backbone_flag=True)`

**Description:** Setup teacher and student models for knowledge distillation.

**Parameters:**
- `num_classes` (int): Total number of classes (COCO + new)
- `device` (torch.device): Device to place models on
- `freeze_backbone_flag` (bool): Whether to freeze backbone during training

**Returns:**
- `teacher_model` (torch.nn.Module): COCO pretrained teacher model (frozen)
- `student_model` (torch.nn.Module): Student model for incremental learning

---

## Dataset Classes

### `CustomDataset(Dataset)`

**Description:** PyTorch Dataset class for loading images and Pascal VOC XML annotations.

**Constructor Parameters:**
- `images_dir` (str): Directory containing image files
- `annotations_dir` (str): Directory containing XML annotation files
- `class_names` (list): List of class names (excluding background)
- `transform` (callable, optional): Image transformations

**Methods:**
- `__len__()`: Returns dataset size
- `__getitem__(idx)`: Returns (image, target) tuple
- `parse_xml_annotation(xml_path)`: Parse Pascal VOC XML file

**Target Format:**
```python
target = {
    'boxes': torch.Tensor,    # Shape: (N, 4) - [x1, y1, x2, y2]
    'labels': torch.Tensor,   # Shape: (N,) - class indices
    'image_id': torch.Tensor, # Shape: (1,) - image identifier
    'area': torch.Tensor,     # Shape: (N,) - box areas
    'iscrowd': torch.Tensor   # Shape: (N,) - crowd flags (all zeros)
}
```

**Example:**
```python
dataset = CustomDataset(
    images_dir='data/images',
    annotations_dir='data/annotations',
    class_names=['person', 'car', 'bicycle'],
    transform=get_default_transforms()
)

image, target = dataset[0]
print(f"Image shape: {image.shape}")
print(f"Number of objects: {len(target['boxes'])}")
```

---

## Utility Functions

### `get_class_names(new_classes=None)`

**Description:** Generate combined class names list (COCO + new classes).

**Parameters:**
- `new_classes` (list, optional): New class names to add. Default: `['traffic_sign']`

**Returns:**
- `all_classes` (list): Combined class names starting with '__background__'
- `num_classes` (int): Total number of classes

**Example:**
```python
classes, num = get_class_names(['stop_sign', 'yield_sign'])
print(f"Total classes: {num}")  # 83 (81 COCO + 2 new)
print(f"New classes at indices: {len(COCO_CLASSES)}-{num-1}")
```

---

### `get_default_transforms(normalize=True)`

**Description:** Get standard image preprocessing transforms.

**Parameters:**
- `normalize` (bool): Apply ImageNet normalization

**Returns:**
- `transforms` (torchvision.transforms.Compose): Transform pipeline

**Transform Pipeline:**
1. `ToTensor()`: Convert PIL Image to tensor, scale to [0, 1]
2. `Normalize()` (optional): Apply ImageNet normalization

**ImageNet Normalization:**
- Mean: [0.485, 0.456, 0.406] (RGB)
- Std: [0.229, 0.224, 0.225] (RGB)

---

### `denormalize_image(tensor, mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])`

**Description:** Reverse ImageNet normalization for visualization.

**Parameters:**
- `tensor` (torch.Tensor): Normalized image tensor
- `mean` (list): Normalization mean values
- `std` (list): Normalization standard deviation values

**Returns:**
- `tensor` (torch.Tensor): Denormalized tensor in [0, 1] range

**Formula:**
```
denormalized = (normalized * std) + mean
```

---

### `freeze_backbone(model, freeze=True)`

**Description:** Freeze or unfreeze model backbone parameters.

**Parameters:**
- `model` (torch.nn.Module): Model to modify
- `freeze` (bool): Whether to freeze (True) or unfreeze (False)

**Effect:**
- Frozen parameters: `requires_grad = False`
- Unfrozen parameters: `requires_grad = True`

---

### `collate_fn(batch)`

**Description:** Custom collate function for DataLoader to handle variable-sized targets.

**Parameters:**
- `batch` (list): List of (image, target) tuples

**Returns:**
- `images` (list): List of image tensors
- `targets` (list): List of target dictionaries

---

## Configuration Functions

### `get_default_config()`

**Description:** Get default training configuration dictionary.

**Returns:**
- `config` (dict): Configuration with default values

**Default Configuration:**
```python
{
    'dataset_path': '/path/to/dataset',
    'batch_size': 2,
    'num_epochs': 10,
    'learning_rate': 0.001,
    'momentum': 0.9,
    'weight_decay': 0.0005,
    'device': torch.device('cuda' if available else 'cpu'),
    'distillation_alpha': 0.5,
    'distillation_temperature': 4.0,
    'save_model_path': 'faster_rcnn_incremental.pth',
    'visualization_save_path': 'results'
}
```

**Parameter Descriptions:**
- `distillation_alpha`: Balance between new learning (0.0) and knowledge retention (1.0)
- `distillation_temperature`: Softmax temperature for knowledge distillation
- `batch_size`: Reduce if GPU memory limited
- `learning_rate`: Adam optimizer learning rate

---

## Visualization Functions

### `visualize_predictions(image, prediction, target, class_names, save_path=None, threshold=0.1)`

**Description:** Create side-by-side visualization of ground truth and predictions.

**Parameters:**
- `image` (torch.Tensor or numpy.ndarray): Input image
- `prediction` (dict): Model predictions
- `target` (dict): Ground truth annotations
- `class_names` (list): Class names for labeling
- `save_path` (str, optional): Path to save visualization
- `threshold` (float): Confidence threshold for displaying predictions

**Features:**
- Automatic image denormalization
- Color-coded bounding boxes
- Confidence scores on predictions
- Side-by-side ground truth vs predictions

---

### `run_inference(model, dataset, device, class_names, config, num_samples=3, threshold=0.3)`

**Description:** Run inference on dataset samples and generate visualizations.

**Parameters:**
- `model` (torch.nn.Module): Trained model
- `dataset` (Dataset): Dataset to sample from
- `device` (torch.device): Device for inference
- `class_names` (list): Class names
- `config` (dict): Configuration with visualization settings
- `num_samples` (int): Number of samples to visualize
- `threshold` (float): Confidence threshold

**Output:**
- Saves visualization images to `config['visualization_save_path']`
- Prints detection statistics

---

### `evaluate_model(model, data_loader, device)`

**Description:** Evaluate model on a dataset and return predictions.

**Parameters:**
- `model` (torch.nn.Module): Model to evaluate
- `data_loader` (DataLoader): Evaluation data loader
- `device` (torch.device): Device for evaluation

**Returns:**
- `all_predictions` (list): List of prediction dictionaries
- `all_targets` (list): List of ground truth targets

**Usage:**
```python
predictions, targets = evaluate_model(model, test_loader, device)

# Calculate metrics
from torchmetrics.detection import MeanAveragePrecision
metric = MeanAveragePrecision()
metric.update(predictions, targets)
map_score = metric.compute()
```

---

## Constants

### `COCO_CLASSES`
**Description:** List of 81 COCO dataset class names (including '__background__').

**Content:** 
- Index 0: '__background__'
- Indices 1-80: COCO object classes (person, bicycle, car, ...)

**Usage:**
```python
print(f"COCO has {len(COCO_CLASSES)} classes")
print(f"Person is at index: {COCO_CLASSES.index('person')}")
```

---

## Error Handling

Most functions include comprehensive error handling:

- **File not found**: Clear error messages for missing models/images
- **GPU memory**: Automatic fallback suggestions
- **Invalid annotations**: Skips corrupted XML files with warnings
- **Empty datasets**: Graceful handling with informative messages

**Example Error Handling:**
```python
try:
    model, classes = load_trained_model('model.pth', device)
except FileNotFoundError:
    print("Model file not found. Please train a model first.")
except RuntimeError as e:
    if "CUDA out of memory" in str(e):
        print("GPU memory error. Try reducing batch_size or use CPU.")
    else:
        raise e
```

---

This API reference covers all major functions and classes in the `faster_rcnn_incremental.py` module. For implementation details and advanced usage, refer to the source code and main documentation.

# Faster R-CNN Incremental Learning Documentation

## Overview

This module implements **Faster R-CNN with Incremental Learning** using **Knowledge Distillation** to retain COCO class detection capabilities while learning new custom classes. The implementation is modular, allowing separate training, inference, and visualization workflows.

## Table of Contents

1. [Key Features](#key-features)
2. [Architecture](#architecture)
3. [Installation & Setup](#installation--setup)
4. [Quick Start](#quick-start)
5. [API Reference](#api-reference)
6. [Configuration](#configuration)
7. [Dataset Format](#dataset-format)
8. [Training Process](#training-process)
9. [Inference & Visualization](#inference--visualization)
10. [Troubleshooting](#troubleshooting)
11. [Examples](#examples)

## Key Features

### ✅ **Incremental Learning**
- Add new object classes while retaining existing COCO knowledge
- Knowledge distillation prevents catastrophic forgetting
- Teacher-student model architecture

### ✅ **Modular Design**
- Separate functions for training, inference, and visualization
- Easy to integrate into existing workflows
- Configurable parameters

### ✅ **Comprehensive Visualization**
- Automatic bounding box visualization
- Confidence score display
- Batch processing of image folders
- Proper image denormalization for correct colors

### ✅ **Production Ready**
- Model checkpointing and resuming
- Error handling and validation
- GPU/CPU compatibility
- Extensive logging

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Teacher Model │    │  Student Model  │
│  (COCO Trained) │    │ (COCO + New)    │
│                 │    │                 │
│  80 Classes     │───▶│  80 + N Classes │
│  Frozen         │    │  Trainable      │
└─────────────────┘    └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────────────────────────────┐
│        Knowledge Distillation           │
│                                         │
│  Loss = α × Distillation_Loss +         │
│         (1-α) × Classification_Loss     │
└─────────────────────────────────────────┘
```

## Installation & Setup

### Prerequisites
```bash
pip install torch torchvision tqdm matplotlib pillow numpy
```

### GPU Support (Recommended)
```bash
# For CUDA 11.8
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### File Structure
```
project/
├── faster_rcnn_incremental.py    # Main module
├── dataset/                      # Training data
│   ├── images/                   # Image files
│   └── annotations/              # XML annotation files
├── test_images/                  # Test images for inference
└── results/                      # Output visualizations
```

## Quick Start

### 1. Training a New Model
```python
from faster_rcnn_incremental import main_training_pipeline

# Train with custom classes
model, class_names, losses = main_training_pipeline(
    dataset_path='path/to/your/dataset',
    new_classes=['traffic_sign', 'custom_object'],
    config={
        'num_epochs': 15,
        'batch_size': 4,
        'learning_rate': 0.001
    }
)
```

### 2. Running Inference on Single Image
```python
from faster_rcnn_incremental import load_model_and_run_inference

results, image, class_names = load_model_and_run_inference(
    model_path='faster_rcnn_incremental.pth',
    image_path='test_image.jpg',
    threshold=0.3
)
```

### 3. Batch Processing Folder
```python
from faster_rcnn_incremental import load_model_and_inference_folder

results = load_model_and_inference_folder(
    model_path='faster_rcnn_incremental.pth',
    folder_path='test_images/',
    output_folder='results/',
    threshold=0.3
)
```

## API Reference

### Core Functions

#### `main_training_pipeline(dataset_path, new_classes=None, config=None)`
**Main training function with knowledge distillation**

**Parameters:**
- `dataset_path` (str): Path to dataset folder containing images/ and annotations/
- `new_classes` (list, optional): List of new class names to learn. Default: ['traffic_sign']
- `config` (dict, optional): Training configuration. Uses defaults if None

**Returns:**
- `model` (torch.nn.Module): Trained model
- `class_names` (list): List of all class names (COCO + new)
- `train_losses` (list): Training loss history

**Example:**
```python
model, classes, losses = main_training_pipeline(
    dataset_path='/data/traffic_signs',
    new_classes=['stop_sign', 'yield_sign'],
    config={'num_epochs': 20, 'batch_size': 4}
)
```

---

#### `load_model_and_run_inference(model_path, image_path, device=None, threshold=0.3)`
**Load trained model and run inference on single image**

**Parameters:**
- `model_path` (str): Path to saved model (.pth file)
- `image_path` (str): Path to input image
- `device` (torch.device, optional): Device to run on. Auto-detects if None
- `threshold` (float): Confidence threshold for detections. Default: 0.3

**Returns:**
- `results` (dict): Detection results with boxes, scores, labels, class_names
- `original_image` (PIL.Image): Original input image
- `class_names` (list): List of all class names

**Example:**
```python
results, img, classes = load_model_and_run_inference(
    'model.pth', 'test.jpg', threshold=0.5
)

print(f"Found {len(results['scores'])} detections")
for i, (box, score, cls) in enumerate(zip(
    results['boxes'], results['scores'], results['class_names']
)):
    print(f"{i+1}. {cls}: {score:.3f}")
```

---

#### `load_model_and_inference_folder(model_path, folder_path, output_folder='inference_results', device=None, threshold=0.3, image_extensions=None)`
**Batch inference on all images in a folder with automatic visualization**

**Parameters:**
- `model_path` (str): Path to saved model
- `folder_path` (str): Path to folder containing images
- `output_folder` (str): Output folder for visualizations. Default: 'inference_results'
- `device` (torch.device, optional): Device to run on
- `threshold` (float): Confidence threshold. Default: 0.3
- `image_extensions` (list, optional): Supported file extensions. Default: ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

**Returns:**
- `results` (list): List of detection results for each image

**Example:**
```python
results = load_model_and_inference_folder(
    model_path='model.pth',
    folder_path='test_images/',
    output_folder='detections/',
    threshold=0.1  # Lower threshold for more detections
)

# Analyze results
total_detections = sum(r['num_detections'] for r in results)
print(f"Total detections across all images: {total_detections}")
```

---

#### `load_trained_model(model_path, device, default_classes=None)`
**Load a trained model from checkpoint**

**Parameters:**
- `model_path` (str): Path to model checkpoint
- `device` (torch.device): Device to load model on
- `default_classes` (list, optional): Default class names if not in checkpoint

**Returns:**
- `model` (torch.nn.Module): Loaded model in eval mode
- `class_names` (list): Class names from checkpoint

---

### Utility Functions

#### `get_class_names(new_classes=None)`
**Get combined class names (COCO + new classes)**

**Parameters:**
- `new_classes` (list, optional): New classes to add. Default: ['traffic_sign']

**Returns:**
- `all_classes` (list): Combined class list
- `num_classes` (int): Total number of classes

#### `get_default_config()`
**Get default training configuration**

**Returns:**
- `config` (dict): Default configuration dictionary

#### `create_model(num_classes, pretrained=True)`
**Create Faster R-CNN model**

**Parameters:**
- `num_classes` (int): Number of output classes
- `pretrained` (bool): Use pretrained backbone. Default: True

**Returns:**
- `model` (torch.nn.Module): Faster R-CNN model

#### `get_default_transforms(normalize=True)`
**Get image preprocessing transforms**

**Parameters:**
- `normalize` (bool): Apply ImageNet normalization. Default: True

**Returns:**
- `transforms` (torchvision.transforms.Compose): Transform pipeline

#### `denormalize_image(tensor, mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])`
**Reverse ImageNet normalization for visualization**

**Parameters:**
- `tensor` (torch.Tensor): Normalized image tensor
- `mean` (list): Normalization mean values
- `std` (list): Normalization std values

**Returns:**
- `tensor` (torch.Tensor): Denormalized tensor

## Configuration

### Default Configuration
```python
{
    'dataset_path': '/path/to/dataset',
    'batch_size': 2,
    'num_epochs': 10,
    'learning_rate': 0.001,
    'momentum': 0.9,
    'weight_decay': 0.0005,
    'device': torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
    'distillation_alpha': 0.5,      # Knowledge distillation weight
    'distillation_temperature': 4.0, # Temperature for softmax
    'save_model_path': 'faster_rcnn_incremental.pth',
    'visualization_save_path': 'results'
}
```

### Key Parameters

#### **Training Parameters**
- `batch_size`: Number of images per batch (reduce if GPU memory limited)
- `num_epochs`: Training epochs (increase for better performance)
- `learning_rate`: Learning rate (0.001 is good default)

#### **Knowledge Distillation Parameters**
- `distillation_alpha`: Balance between new learning and knowledge retention
  - 0.0: Only new learning (may forget COCO)
  - 0.5: Balanced (recommended)
  - 1.0: Only knowledge distillation (won't learn new classes)
- `distillation_temperature`: Softmax temperature for knowledge transfer
  - Higher values (4.0-8.0): Softer probability distributions
  - Lower values (1.0-2.0): Sharper distributions

## Dataset Format

### Directory Structure
```
dataset/
├── images/
│   ├── image001.jpg
│   ├── image002.png
│   └── ...
└── annotations/
    ├── image001.xml
    ├── image002.xml
    └── ...
```

### XML Annotation Format (Pascal VOC)
```xml
<annotation>
    <filename>image001.jpg</filename>
    <size>
        <width>640</width>
        <height>480</height>
        <depth>3</depth>
    </size>
    <object>
        <name>traffic_sign</name>
        <bndbox>
            <xmin>100</xmin>
            <ymin>150</ymin>
            <xmax>200</xmax>
            <ymax>250</ymax>
        </bndbox>
    </object>
</annotation>
```

### Supported Image Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff)

## Training Process

### 1. **Model Setup**
```python
# Teacher model (COCO pretrained, frozen)
teacher_model = create_model(len(COCO_CLASSES), pretrained=True)
teacher_model.eval()

# Student model (COCO + new classes, trainable)
student_model = create_model(total_classes, pretrained=True)
```

### 2. **Knowledge Distillation Loss**
```python
total_loss = (1 - α) × student_loss + α × distillation_loss

where:
- student_loss: Standard detection loss on new data
- distillation_loss: KL divergence between teacher and student predictions
- α: distillation_alpha parameter
```

### 3. **Training Loop**
```python
for epoch in range(num_epochs):
    for batch in dataloader:
        # Student forward pass
        student_loss = student_model(images, targets)
        
        # Teacher predictions (frozen)
        teacher_predictions = teacher_model(images)
        
        # Student predictions (for distillation)
        student_predictions = student_model(images)
        
        # Combine losses
        total_loss = combine_losses(student_loss, teacher_predictions, 
                                  student_predictions, alpha, temperature)
        
        # Backward pass
        total_loss.backward()
        optimizer.step()
```

### 4. **Checkpointing**
- Automatic checkpoints every 5 epochs
- Final model saved with class names
- Training loss history included

## Inference & Visualization

### Detection Results Format
```python
results = {
    'boxes': numpy.ndarray,      # Shape: (N, 4) - [x1, y1, x2, y2]
    'scores': numpy.ndarray,     # Shape: (N,) - confidence scores
    'labels': numpy.ndarray,     # Shape: (N,) - class indices
    'class_names': list          # Length: N - class name strings
}
```

### Visualization Features
- **Predictions-only display** (clean, focused layout)
- **Non-Maximum Suppression (NMS)** filtering for overlapping boxes
- **Improved text visibility** with white text on colored backgrounds
- **Color-coded bounding boxes** by class
- **Smart text positioning** (above box when space allows)
- **Confidence scores** displayed with 3 decimal precision
- **Automatic image denormalization** for correct colors
- **High-resolution output** (200 DPI) for crisp images

### Batch Processing Results
```python
folder_results = [
    {
        'image_path': 'path/to/image1.jpg',
        'num_detections': 3,
        'detections': results_dict,
        'visualization_path': 'output/image1_detected.jpg'
    },
    # ... more results
]
```

## Troubleshooting

### Common Issues

#### **1. Only New Classes Detected**
**Problem:** Model detects traffic_sign but not person, car, etc.

**Solution:**
```python
# Check if knowledge distillation was used during training
# Retrain with proper distillation:
model, classes, losses = main_training_pipeline(
    dataset_path='your_data',
    new_classes=['traffic_sign'],
    config={'distillation_alpha': 0.3}  # Enable distillation
)
```

#### **2. Poor Detection Performance**
**Problem:** Low mAP, missed detections

**Solutions:**
- Lower confidence threshold: `threshold=0.1`
- Increase training epochs: `num_epochs=20`
- Reduce batch size if GPU memory issues: `batch_size=1`
- Check dataset quality and annotations

#### **3. Visualization Colors Look Wrong**
**Problem:** Images have weird colors in output

**Solution:** Fixed in latest version with proper denormalization detection
```python
# The fix automatically detects normalized images:
if image_np.min() < 0 or image_np.max() > 1.0:
    # Apply denormalization
```

#### **4. CUDA Out of Memory**
**Problem:** GPU memory error during training

**Solutions:**
```python
config = {
    'batch_size': 1,        # Reduce batch size
    'num_workers': 0,       # Reduce data loading workers
}

# Or use CPU:
config['device'] = torch.device('cpu')
```

#### **5. Model File Not Found**
**Problem:** Cannot load saved model

**Check:**
```python
import os
print("Available .pth files:")
for f in os.listdir('.'):
    if f.endswith('.pth'):
        print(f"  {f}")
```

### Performance Optimization

#### **Training Speed**
- Use GPU: `device='cuda'`
- Increase batch size: `batch_size=4` (if memory allows)
- Freeze backbone: Set `freeze_backbone=True` in training

#### **Inference Speed**
- Use GPU for inference
- Batch process multiple images
- Lower input resolution (trade-off with accuracy)

#### **Memory Usage**
- Reduce batch size
- Use mixed precision training
- Clear cache: `torch.cuda.empty_cache()`

## Examples

### Example 1: Complete Training Pipeline
```python
from faster_rcnn_incremental import main_training_pipeline

# Configuration for traffic sign detection
config = {
    'dataset_path': '/data/traffic_signs',
    'batch_size': 4,
    'num_epochs': 15,
    'learning_rate': 0.001,
    'distillation_alpha': 0.3,
    'save_model_path': 'traffic_sign_detector.pth'
}

# Train model
model, class_names, losses = main_training_pipeline(
    dataset_path=config['dataset_path'],
    new_classes=['stop_sign', 'yield_sign', 'speed_limit'],
    config=config
)

print(f"Training completed!")
print(f"Model can detect {len(class_names)} classes")
print(f"Final loss: {losses[-1]:.4f}")
```

### Example 2: Inference with Multiple Thresholds
```python
from faster_rcnn_incremental import load_model_and_inference_folder

model_path = 'traffic_sign_detector.pth'
test_folder = 'test_images'

# Test with different confidence thresholds
thresholds = [0.1, 0.3, 0.5, 0.7]

for threshold in thresholds:
    print(f"\nTesting with threshold {threshold}")
    
    results = load_model_and_inference_folder(
        model_path=model_path,
        folder_path=test_folder,
        output_folder=f'results_thresh_{threshold}',
        threshold=threshold
    )
    
    # Analyze results
    total_detections = sum(r['num_detections'] for r in results)
    print(f"Total detections: {total_detections}")
    
    # Count by class
    class_counts = {}
    for result in results:
        for class_name in result['detections']['class_names']:
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
    
    print("Detections by class:")
    for cls, count in sorted(class_counts.items()):
        print(f"  {cls}: {count}")
```

### Example 3: Custom Dataset Integration
```python
from faster_rcnn_incremental import CustomDataset, get_default_transforms

# Create custom dataset
transform = get_default_transforms(normalize=True)
dataset = CustomDataset(
    images_dir='custom_data/images',
    annotations_dir='custom_data/annotations',
    class_names=['custom_class1', 'custom_class2'],
    transform=transform
)

print(f"Dataset size: {len(dataset)}")
print(f"Classes: {dataset.class_names}")

# Use in training
from torch.utils.data import DataLoader
dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
```

### Example 4: Model Evaluation
```python
from faster_rcnn_incremental import load_trained_model, evaluate_model

# Load model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model, class_names = load_trained_model('model.pth', device)

# Create test dataset
test_dataset = CustomDataset(
    images_dir='test/images',
    annotations_dir='test/annotations',
    class_names=class_names[1:],  # Exclude background
    transform=get_default_transforms()
)

test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)

# Evaluate
predictions, targets = evaluate_model(model, test_loader, device)
print(f"Evaluated on {len(predictions)} images")
```

---

## Advanced Usage

### Custom Loss Functions
```python
class CustomDistillationLoss(nn.Module):
    def __init__(self, alpha=0.5, temperature=4.0, focal_alpha=0.25):
        super().__init__()
        self.alpha = alpha
        self.temperature = temperature
        self.focal_alpha = focal_alpha
    
    def forward(self, student_logits, teacher_logits, targets):
        # Implement custom loss logic
        pass
```

### Model Ensemble
```python
# Load multiple models
models = []
for model_path in ['model1.pth', 'model2.pth', 'model3.pth']:
    model, _ = load_trained_model(model_path, device)
    models.append(model)

# Ensemble prediction
def ensemble_predict(models, image_tensor):
    predictions = []
    for model in models:
        pred = model(image_tensor)[0]
        predictions.append(pred)
    
    # Combine predictions (e.g., average scores)
    return combine_predictions(predictions)
```

### Transfer Learning from Custom Pretrained
```python
# Load your own pretrained model as teacher
teacher_model = load_trained_model('custom_pretrained.pth', device)[0]
teacher_model.eval()

# Use in training pipeline
model, classes, losses = main_training_pipeline(
    dataset_path='new_data',
    new_classes=['new_class'],
    teacher_model=teacher_model  # Custom teacher
)
```

---

This documentation provides comprehensive coverage of the Faster R-CNN incremental learning module. For additional questions or advanced use cases, refer to the source code comments and docstrings.

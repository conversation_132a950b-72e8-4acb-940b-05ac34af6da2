# Quick Reference Guide - Faster R-CNN Incremental Learning

## 🚀 Quick Start Commands

### 1. Train a New Model
```python
from faster_rcnn_incremental import main_training_pipeline

# Basic training
model, classes, losses = main_training_pipeline(
    dataset_path='path/to/your/dataset',
    new_classes=['your_class1', 'your_class2']
)
```

### 2. Run Inference on Single Image
```python
from faster_rcnn_incremental import load_model_and_run_inference

results, img, classes = load_model_and_run_inference(
    'model.pth', 'test_image.jpg', threshold=0.3
)
```

### 3. Process Folder of Images
```python
from faster_rcnn_incremental import load_model_and_inference_folder

results = load_model_and_inference_folder(
    'model.pth', 'test_images/', 'output/', threshold=0.3
)
```

---

## 📁 Required Folder Structure

```
project/
├── dataset/
│   ├── images/           # Your training images
│   │   ├── img001.jpg
│   │   └── img002.png
│   └── annotations/      # Pascal VOC XML files
│       ├── img001.xml
│       └── img002.xml
├── test_images/          # Images for testing
└── faster_rcnn_incremental.py
```

---

## ⚙️ Common Configurations

### Training Configuration
```python
config = {
    'batch_size': 4,              # Reduce if GPU memory issues
    'num_epochs': 15,             # Increase for better performance
    'learning_rate': 0.001,       # Standard learning rate
    'distillation_alpha': 0.3,    # 30% knowledge retention
    'distillation_temperature': 4.0,
    'save_model_path': 'my_model.pth'
}
```

### Inference Thresholds
- `threshold=0.1`: More detections (may include false positives)
- `threshold=0.3`: Balanced (recommended)
- `threshold=0.5`: High confidence only
- `threshold=0.7`: Very conservative

---

## 🔧 Common Issues & Solutions

### Issue: Only New Classes Detected
```python
# Solution: Retrain with knowledge distillation
config = {'distillation_alpha': 0.3}  # Enable distillation
model, classes, losses = main_training_pipeline(
    dataset_path='your_data',
    new_classes=['your_class'],
    config=config
)
```

### Issue: GPU Out of Memory
```python
# Solution: Reduce batch size
config = {
    'batch_size': 1,
    'device': torch.device('cpu')  # Or use CPU
}
```

### Issue: Poor Detection Performance
```python
# Solution: Lower threshold and train longer
results = load_model_and_inference_folder(
    'model.pth', 'images/', 'output/', threshold=0.1
)

# Or train with more epochs
config = {'num_epochs': 25}
```

### Issue: Weird Colors in Visualizations
**Fixed in latest version** - automatic denormalization detection

---

## 📊 Understanding Results

### Detection Results Format
```python
results = {
    'boxes': [[x1, y1, x2, y2], ...],     # Bounding boxes
    'scores': [0.95, 0.87, ...],          # Confidence scores
    'labels': [1, 15, ...],               # Class indices
    'class_names': ['person', 'cat', ...] # Class names
}
```

### Analyzing Batch Results
```python
results = load_model_and_inference_folder(...)

# Count total detections
total = sum(r['num_detections'] for r in results)

# Count by class
class_counts = {}
for result in results:
    for cls in result['detections']['class_names']:
        class_counts[cls] = class_counts.get(cls, 0) + 1

print(f"Total detections: {total}")
for cls, count in sorted(class_counts.items()):
    print(f"{cls}: {count}")
```

---

## 🎯 Best Practices

### Training
1. **Start small**: Use `batch_size=2` and `num_epochs=10` for testing
2. **Enable distillation**: Set `distillation_alpha=0.3` to retain COCO knowledge
3. **Monitor loss**: Check that training loss decreases over epochs
4. **Use GPU**: Significantly faster than CPU training

### Dataset Preparation
1. **Quality annotations**: Ensure XML files match image names exactly
2. **Balanced data**: Include diverse examples of each class
3. **Consistent naming**: Use consistent class names in XML files

### Inference
1. **Test thresholds**: Try different confidence thresholds (0.1, 0.3, 0.5)
2. **Batch processing**: Use folder inference for multiple images
3. **Check visualizations**: Verify detections look correct

### Performance Optimization
1. **Freeze backbone**: Faster training with `freeze_backbone=True`
2. **Increase batch size**: Better GPU utilization if memory allows
3. **Mixed precision**: Use `torch.cuda.amp` for faster training

---

## 📝 XML Annotation Format

```xml
<annotation>
    <filename>image001.jpg</filename>
    <size>
        <width>640</width>
        <height>480</height>
        <depth>3</depth>
    </size>
    <object>
        <name>your_class_name</name>
        <bndbox>
            <xmin>100</xmin>
            <ymin>150</ymin>
            <xmax>200</xmax>
            <ymax>250</ymax>
        </bndbox>
    </object>
    <!-- More objects... -->
</annotation>
```

---

## 🧪 Testing Your Model

### Check Model Classes
```python
from faster_rcnn_incremental import load_trained_model

model, classes = load_trained_model('model.pth', device)
print(f"Model can detect {len(classes)} classes:")
for i, cls in enumerate(classes[:10]):  # First 10
    print(f"{i}: {cls}")
```

### Test on Single Image
```python
results, img, classes = load_model_and_run_inference(
    'model.pth', 'test.jpg', threshold=0.1
)

print(f"Found {len(results['scores'])} detections:")
for box, score, cls in zip(results['boxes'], results['scores'], results['class_names']):
    print(f"  {cls}: {score:.3f}")
```

### Verify Both COCO and New Classes
```python
# Check for COCO classes
coco_classes = ['person', 'car', 'bicycle', 'dog']
new_classes = ['your_custom_class']

print("COCO classes found:")
for cls in coco_classes:
    if cls in classes:
        print(f"  ✓ {cls}")
    else:
        print(f"  ✗ {cls}")

print("New classes found:")
for cls in new_classes:
    if cls in classes:
        print(f"  ✓ {cls}")
    else:
        print(f"  ✗ {cls}")
```

---

## 🔍 Debugging Tips

### Check Dataset Loading
```python
from faster_rcnn_incremental import CustomDataset, get_default_transforms

dataset = CustomDataset(
    'data/images', 'data/annotations', 
    ['your_class'], get_default_transforms()
)

print(f"Dataset size: {len(dataset)}")
if len(dataset) > 0:
    img, target = dataset[0]
    print(f"First image shape: {img.shape}")
    print(f"First target boxes: {target['boxes'].shape}")
```

### Monitor Training Progress
```python
model, classes, losses = main_training_pipeline(...)

# Plot training loss
import matplotlib.pyplot as plt
plt.plot(losses)
plt.title('Training Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.show()

print(f"Final loss: {losses[-1]:.4f}")
print(f"Loss reduction: {(losses[0] - losses[-1]) / losses[0] * 100:.1f}%")
```

### Check GPU Usage
```python
import torch

print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
```

---

## 📚 Additional Resources

### File Locations
- **Main Documentation**: `DOCUMENTATION.md`
- **API Reference**: `API_REFERENCE.md`
- **Example Scripts**: `complete_pipeline.py`, `test_existing_model.py`

### Key Functions Summary
- `main_training_pipeline()`: Train new model
- `load_model_and_run_inference()`: Single image inference
- `load_model_and_inference_folder()`: Batch inference
- `load_trained_model()`: Load saved model
- `get_class_names()`: Get class list

### Configuration Keys
- `batch_size`: Images per batch
- `num_epochs`: Training epochs
- `distillation_alpha`: Knowledge retention weight
- `threshold`: Confidence threshold for detections

---

This quick reference covers the most common use cases and issues. For detailed explanations, see the full documentation files.

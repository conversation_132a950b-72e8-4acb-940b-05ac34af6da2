# Faster R-CNN Incremental Learning - Modular Implementation

This is a modular implementation of Faster R-CNN with incremental learning capabilities using knowledge distillation. The code has been refactored into callable functions for easy training, inference, and visualization.

## Features

- **Modular Design**: Separate functions for training, inference, and visualization
- **Knowledge Distillation**: Retains COCO class detection while learning new classes
- **Easy Configuration**: Customizable training parameters
- **Flexible Usage**: Can be used as a library or standalone script
- **Comprehensive Visualization**: Automatic generation of prediction visualizations

## Main Functions

### 1. Training Functions

#### `main_training_pipeline(dataset_path, new_classes=None, config=None)`
Complete training pipeline that handles everything from dataset loading to model saving.

**Parameters:**
- `dataset_path`: Path to your dataset folder
- `new_classes`: List of new class names (optional, defaults to ['traffic_sign'])
- `config`: Training configuration dictionary (optional)

**Returns:**
- `model`: Trained PyTorch model
- `class_names`: List of all class names (COCO + new classes)
- `train_losses`: List of training losses per epoch

### 2. Inference Functions

#### `load_model_and_run_inference(model_path, image_path, device=None, threshold=0.3)`
Load a trained model and run inference on a single image.

**Parameters:**
- `model_path`: Path to saved model file (.pth)
- `image_path`: Path to image for inference
- `device`: Computing device (optional, auto-detected)
- `threshold`: Confidence threshold for predictions (default: 0.3)

**Returns:**
- `results`: Dictionary with boxes, labels, scores, and class names
- `original_image`: PIL Image object
- `class_names`: List of class names

#### `run_inference(model, dataset, device, class_names, config, num_samples=3, threshold=0.3)`
Run inference on a dataset and generate visualizations.

### 3. Utility Functions

#### `get_default_config()`
Returns default training configuration.

#### `get_class_names(new_classes=None)`
Returns combined COCO and new class names.

#### `create_dataset(dataset_path, class_names, transforms=None)`
Creates a dataset object from the given path.

## Usage Examples

### Basic Training
```python
from faster_rcnn_incremental import main_training_pipeline

# Train with default settings
model, classes, losses = main_training_pipeline(
    dataset_path='/path/to/your/dataset',
    new_classes=['traffic_sign', 'road_sign']
)
```

### Custom Training Configuration
```python
from faster_rcnn_incremental import main_training_pipeline, get_default_config

# Customize configuration
config = get_default_config()
config['num_epochs'] = 15
config['batch_size'] = 4
config['learning_rate'] = 0.0005

# Train with custom config
model, classes, losses = main_training_pipeline(
    dataset_path='/path/to/your/dataset',
    new_classes=['custom_class1', 'custom_class2'],
    config=config
)
```

### Single Image Inference
```python
from faster_rcnn_incremental import load_model_and_run_inference

# Run inference on a single image
results, image, classes = load_model_and_run_inference(
    model_path='faster_rcnn_incremental.pth',
    image_path='test_image.jpg',
    threshold=0.5
)

# Print results
for i, (box, score, class_name) in enumerate(zip(
    results['boxes'], results['scores'], results['class_names']
)):
    print(f"{i+1}. {class_name}: {score:.3f}")
```

### Dataset Inference with Visualization
```python
from faster_rcnn_incremental import (
    load_trained_model, create_dataset, run_inference, get_default_config
)
import torch

# Load model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model, class_names = load_trained_model('model.pth', device)

# Create dataset
dataset = create_dataset('/path/to/dataset', class_names)

# Run inference and generate visualizations
config = get_default_config()
run_inference(model, dataset, device, class_names, config, num_samples=5)
```

## Dataset Structure

Your dataset should follow this structure:
```
dataset/
├── image1.jpg
├── image1.xml
├── image2.jpg
├── image2.xml
├── image3.png
├── image3.xml
└── ...
```

- Images can be in `.jpg`, `.png`, or `.jpeg` format
- Annotations should be in Pascal VOC XML format
- Each image should have a corresponding XML file with the same name

## Configuration Options

The configuration dictionary supports these parameters:

```python
config = {
    'dataset_path': '/path/to/dataset',
    'batch_size': 2,
    'num_epochs': 10,
    'learning_rate': 0.001,
    'momentum': 0.9,
    'weight_decay': 0.0005,
    'device': torch.device('cuda'),
    'distillation_alpha': 0.5,  # Weight for knowledge distillation
    'distillation_temperature': 4.0,  # Temperature for soft targets
    'save_model_path': 'model.pth',
    'visualization_save_path': 'results'
}
```

## Installation

Install required packages:
```python
from faster_rcnn_incremental import install_packages
install_packages()
```

Or manually install:
```bash
pip install torch torchvision opencv-python matplotlib pillow numpy tqdm albumentations
```

## Example Script

See `example_usage.py` for comprehensive examples of how to use all the functions.

## Output Files

After training, you'll get:
- `faster_rcnn_incremental.pth`: Trained model file
- `results/`: Directory with visualization images
- `training_loss.png`: Training loss plot
- `checkpoint_epoch_X.pth`: Checkpoint files (every 5 epochs)

## Notes

- The model uses knowledge distillation to retain COCO class detection capabilities
- GPU is automatically used if available
- The backbone can be frozen for faster training (default: True)
- Visualizations show ground truth vs predictions side by side
- All functions include error handling and informative messages
